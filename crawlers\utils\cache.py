import time
from typing import Dict, Optional
from datetime import datetime, timedelta


class CookieCache:
    def __init__(self, cookie_refresh_interval: int = 30):
        """
        初始化Cookie缓存管理器

        Args:
            cookie_refresh_interval: Cookie刷新间隔(分钟),默认30分钟
        """

        self.cookies: Dict[str, Dict] = {}  # 存储各平台的cookie信息
        self.refresh_interval = timedelta(minutes=cookie_refresh_interval)

    def add_cookie(self, platform: str, cookie: str) -> None:
        """
        添加或更新cookie

        Args:
            platform: 平台名称
            cookie: cookie字符串
        """
        self.cookies[platform] = {
            "value": cookie,
            "failed_count": 0,
            "last_refresh": datetime.now(),
        }

    def get_cookie(self, platform: str) -> Optional[str]:
        """
        获取指定平台的cookie

        Args:
            platform: 平台名称

        Returns:
            如果cookie有效返回cookie字符串,否则返回None
        """
        if platform not in self.cookies:
            return None

        cookie_info = self.cookies[platform]

        # 检查失败次数
        if cookie_info["failed_count"] >= 3:
            del self.cookies[platform]
            return None

        # 检查是否需要刷新
        if datetime.now() - cookie_info["last_refresh"] > self.refresh_interval:
            del self.cookies[platform]
            return None

        return cookie_info["value"]

    def mark_failed(self, platform: str) -> None:
        """
        标记指定平台的cookie请求失败

        Args:
            platform: 平台名称
        """
        if platform in self.cookies:
            self.cookies[platform]["failed_count"] += 1

    def clear_failed_count(self, platform: str) -> None:
        """
        清除指定平台的cookie失败次数

        Args:
            platform: 平台名称
        """
        if platform in self.cookies:
            self.cookies[platform]["failed_count"] = 0


class ProxyCache:
    def __init__(self, proxy_timeout: int = 300):
        """
        初始化代理缓存管理器

        Args:
            proxy_timeout: 代理超时时间(秒),默认5分钟
        """
        self.proxy = None  # 当前代理
        self.last_refresh = None  # 上次刷新时间
        self.proxy_timeout = proxy_timeout

    def set_proxy(self, proxy: str) -> None:
        """
        设置新的代理

        Args:
            proxy: 代理地址
        """
        self.proxy = proxy
        self.last_refresh = time.time()

    def get_proxy(self) -> Optional[str]:
        """
        获取当前代理

        Returns:
            如果代理有效返回代理地址,否则返回None
        """
        if not self.proxy or not self.last_refresh:
            return None

        # 检查是否过期
        if time.time() - self.last_refresh > self.proxy_timeout:
            self.proxy = None
            return None

        return self.proxy

    def clear(self) -> None:
        """清除当前代理"""
        self.proxy = None
        self.last_refresh = None
