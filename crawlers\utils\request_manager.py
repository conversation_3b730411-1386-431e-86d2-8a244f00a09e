from typing import Op<PERSON>, <PERSON><PERSON>

from crawlers.browser.virtual_browser_api import VirtualBrowser<PERSON>pi, sync_douyin_cookie
from crawlers.cookiesManager.cookie_cache import CookieCache
from crawlers.proxy.qing_guo import QIngGuo
from crawlers.utils.cache import ProxyCache
from crawlers.utils.function import wait
from crawlers.utils.logger import log_setup


class RequestManager:
    def __init__(
            self,
            browser_api_key: str,
            proxy_auth_key: str,
            proxy_auth_password: str,
            cookie_refresh_interval: int = 30,
            proxy_timeout: int = 60,
            cache_type: str = 'memory',
    ):
        """
        初始化请求管理器

        Args:
            browser_api_key: 浏览器API密钥
            proxy_auth_key: 代理认证密钥
            proxy_auth_password: 代理认证密码
            cookie_refresh_interval: cookie刷新间隔(分钟)
            proxy_timeout: 代理超时时间(秒)
            cache_type: 缓存类型，支持 'memory' 或 'redis'
        """
        self.browser_api = VirtualBrowserApi(api_key=browser_api_key)
        self.proxy_client = QIngGuo(
            auth_key=proxy_auth_key, auth_password=proxy_auth_password
        )

        self.cookie_cache = CookieCache(cookie_refresh_interval, cache_type)
        self.proxy_cache = ProxyCache(proxy_timeout)
        self.log = log_setup()

    async def get_cookie(self, platform: str) -> str:
        """
        获取指定平台的cookie,如果缓存中没有有效的cookie则生成新的

        Args:
            platform: 平台名称

        Returns:
            cookie字符串
        """
        cookie = self.cookie_cache.get_cookie(platform)
        if cookie:
            return cookie

        # 从浏览器获取新cookie
        cookie = await self._generate_new_cookie(platform)
        if cookie:
            self.cookie_cache.add_cookie(platform, cookie)
        return cookie

    def get_proxy(self) -> Optional[str]:
        """
        获取代理地址,如果缓存中没有有效的代理则获取新的

        Returns:
            代理地址,如果获取失败则返回None
        """
        proxy = self.proxy_cache.get_proxy()
        if proxy:
            return proxy

        # 从代理服务获取新代理
        proxy = self.proxy_client.query_proxy()
        if proxy:
            self.proxy_cache.set_proxy(proxy)
        return proxy

    async def _generate_new_cookie(self, platform: str) -> Optional[str]:
        """
        为指定平台生成新的cookie

        Args:
            platform: 平台名称

        Returns:
            生成的cookie字符串,如果生成失败则返回None
        """
        try:
            cookies = await sync_douyin_cookie(self.browser_api, platform)
            if not cookies:
                return None

            return cookies

        except Exception as e:
            self.log.error(f"生成{platform} cookie时出错: {e}")
            return None

    def mark_request_failed(self, platform: str) -> None:
        """
        标记请求失败,增加cookie失败计数并清除当前代理

        Args:
            platform: 平台名称
        """
        self.cookie_cache.mark_failed(platform)
        self.proxy_cache.clear()

    def mark_request_success(self, platform: str) -> None:
        """
        标记请求成功,清除cookie失败计数

        Args:
            platform: 平台名称
        """
        self.cookie_cache.clear_failed_count(platform)

    async def prepare_request(
            self, platform: str
    ) -> Tuple[Optional[str], Optional[str]]:
        """
        准备请求所需的cookie和代理

        Args:
            platform: 平台名称

        Returns:
            元组(cookie, proxy),如果获取失败则相应值为None
        """
        cookie = await self.get_cookie(platform)
        proxy = self.get_proxy()
        return cookie, proxy

    async def __record_request_messages(self, url: str, method: str = "GET", headers: dict = None, data: dict = None):
        """
        记录请求信息

        Args:
            url: 请求URL
            method: 请求方法
            headers: 请求头
            data: 请求数据
        """
        self.log.info(f"Request URL: {url}")
        self.log.info(f"Request Method: {method}")
        if headers:
            self.log.info(f"Request Headers: {headers}")
        if data:
            # 记录请求体数据会导致日志文件体积过大，仅记录关键信息
            self.log.info(f"Request Data Keys: {list(data.keys()) if isinstance(data, dict) else 'Non-dict data'}")

    async def __return_response(self, response):
        """
        处理响应并记录响应信息

        Args:
            response: HTTP响应对象

        Returns:
            响应的JSON数据
        """
        self.log.info(f"Response URL: {response.url}")
        self.log.info(f"Response Code: {response.status_code}")
        self.log.info(f"Response Headers: {dict(response.headers)}")
        # 记录请求体数据会导致日志文件体积过大，仅在必要时记录
        # self.log.info(f"Response Content: {response.content}")
        response.raise_for_status()
        await wait()
        # if response.status_code != 200:
        #     self.log.error(f"请求 {url} 失败，响应码 {response.status_code}")
        #     return
        return response.json()