#

import asyncio
import os
import time
import yaml

# 基础爬虫客户端和哔哩哔哩API端点
from crawlers.spiders.base_crawler import BaseCrawler
from crawlers.spiders.bilibili.endpoints import BilibiliAPIEndpoints

# 哔哩哔哩工具类
from crawlers.spiders.bilibili.utils import EndpointGenerator, bv2av, ResponseAnalyzer

# 数据请求模型
from crawlers.spiders.bilibili.models import (
    UserPostVideos,
    UserProfile,
    ComPopular,
    UserDynamic,
    PlayUrl,
)
from crawlers.utils.request_manager import RequestManager

# 配置文件路径
path = os.path.abspath(os.path.dirname(__file__))

# 读取配置文件
with open(f"{path}/config.yaml", "r", encoding="utf-8") as f:
    config = yaml.safe_load(f)


class BilibiliWebCrawler:
    """哔哩哔哩网页爬虫"""

    def __init__(
        self,
        browser_api_key: str,
        proxy_auth_key: str = None,
        proxy_auth_password: str = None,
        cookie_refresh_interval: int = 30,
        proxy_timeout: int = 60,
    ):
        """
        Args:
            browser_api_key: 浏览器API密钥
            proxy_auth_key: 代理认证密钥(可选)
            proxy_auth_password: 代理认证密码(可选)
            cookie_refresh_interval: cookie刷新间隔(分钟)
            proxy_timeout: 代理超时时间(秒)
        """
        self.request_manager = RequestManager(
            browser_api_key=browser_api_key,
            proxy_auth_key=proxy_auth_key,
            proxy_auth_password=proxy_auth_password,
            cookie_refresh_interval=cookie_refresh_interval,
            proxy_timeout=proxy_timeout,
        )
        self.__platform = "blibli"

    async def get_bilibili_headers(self, cookie: str = None, proxy: str = None):
        bili_config = config["TokenManager"]["bilibili"]
        kwargs = {
            "headers": {
                "accept-language": bili_config["headers"]["accept-language"],
                "origin": bili_config["headers"]["origin"],
                "referer": bili_config["headers"]["referer"],
                "user-agent": bili_config["headers"]["user-agent"],
                "cookie": cookie,
            },
            "proxies": {"http://": proxy, "https://": proxy},
        }
        return kwargs

    async def __create_base_crawler(self) -> BaseCrawler | None:
        """
        获取基础爬虫对象
        """
        try:
            cookie, proxy = await self.request_manager.prepare_request(self.__platform)
            kwargs = await self.get_bilibili_headers(cookie=cookie, proxy=proxy)
            base_crawler = BaseCrawler(
                proxies=kwargs["proxies"], crawler_headers=kwargs["headers"]
            )
            return base_crawler
        except Exception as e:
            print(e)
            return None

    async def fetch_one_video(self, bv_id: str) -> dict:
        """
        获取单个视频详情信息
        Args:
            bv_id: 视频bv_id
        """
        try:
            base_crawler = await self.__create_base_crawler()
            if base_crawler is None:
                return {}

            async with base_crawler as crawler:
                endpoint = f"{BilibiliAPIEndpoints.POST_DETAIL}?bvid={bv_id}"
                response = await crawler.fetch_get_json(endpoint)
                if response:
                    self.request_manager.mark_request_success("")
            return response
        except Exception as e:
            print(e)

    async def fetch_video_playurl(self, bv_id: str, cid: str, qn: str = "64") -> dict:
        """
        获取视频流地址
        Args:
            bv_id: 视频bv_id
            cid: 视频cid
            qn: 清晰度
        """
        try:
            base_crawler = await self.__create_base_crawler()
            if base_crawler is None:
                return {}

            async with base_crawler as crawler:
                # 通过模型生成基本请求参数
                params = PlayUrl(bvid=bv_id, cid=cid, qn=qn)
                # 创建请求endpoint
                generator = EndpointGenerator(params.dict())
                endpoint = await generator.video_playurl_endpoint()
                # 发送请求，获取请求响应结果
                response = await crawler.fetch_get_json(endpoint)
            return response
        except Exception as e:
            print(e)

    # 搜素视频
    async def fetch_search_video(
        self,
        keyword: str,
        page: int = 1,
        page_size: int = 20,
        order: str = "",
        pubtime_begin_s: int = 0,
        pubtime_end_s: int = 0,
    ) -> dict:

        try:
            base_crawler = await self.__create_base_crawler()
            if base_crawler is None:
                return {}

            async with base_crawler as crawler:
                wts: str = str(round(time.time()))
                # 通过模型生成基本请求参数
                params = {
                    "search_type": "video",
                    "keyword": keyword,
                    "page": page,
                    "page_size": page_size,
                    "order": order,
                    "pubtime_begin_s": pubtime_begin_s,
                    "pubtime_end_s": pubtime_end_s,
                    "wts": wts,
                }
                # 创建请求endpoint
                generator = EndpointGenerator(params)
                endpoint = await generator.video_search_endpoint()
                # 发送请求，获取请求响应结果
                response = await crawler.fetch_get_json(endpoint)
            return response
        except Exception as e:
            print(e)

    # 获取用户发布视频作品数据
    async def fetch_user_post_videos(self, uid: str, pn: int) -> dict:
        """
        :param uid: 用户uid
        :param pn: 页码
        :return:
        """
        base_crawler = await self.__create_base_crawler()
        if base_crawler is None:
            return {}

        async with base_crawler as crawler:
            # 通过模型生成基本请求参数
            params = UserPostVideos(mid=uid, pn=pn)
            # 创建请求endpoint
            generator = EndpointGenerator(params.dict())
            endpoint = await generator.user_post_videos_endpoint()
            # 发送请求，获取请求响应结果
            response = await crawler.fetch_get_json(endpoint)
        return response

    # 获取用户所有收藏夹信息
    async def fetch_collect_folders(self, uid: str) -> dict:

        try:
            base_crawler = await self.__create_base_crawler()
            if base_crawler is None:
                return {}

            async with base_crawler as crawler:
                # 创建请求endpoint
                endpoint = f"{BilibiliAPIEndpoints.COLLECT_FOLDERS}?up_mid={uid}"
                # 发送请求，获取请求响应结果
                response = await crawler.fetch_get_json(endpoint)
            # 分析响应结果
            result_dict = await ResponseAnalyzer.collect_folders_analyze(
                response=response
            )
            return result_dict
        except Exception as e:
            print(e)
            return {}

    # 获取指定收藏夹内视频数据
    async def fetch_folder_videos(self, folder_id: str, pn: int) -> dict:
        """
        :param folder_id: 收藏夹id-- 可从<获取用户所有收藏夹信息>获得
        :param pn: 页码
        :return:
        """
        # 获取请求头信息
        kwargs = await self.get_bilibili_headers()
        # 创建基础爬虫对象
        base_crawler = BaseCrawler(
            proxies=kwargs["proxies"], crawler_headers=kwargs["headers"]
        )
        # 发送请求，获取请求响应结果
        async with base_crawler as crawler:
            endpoint = f"{BilibiliAPIEndpoints.COLLECT_VIDEOS}?media_id={folder_id}&pn={pn}&ps=20&keyword=&order=mtime&type=0&tid=0&platform=web"
            response = await crawler.fetch_get_json(endpoint)
        return response

    # 获取指定用户的信息
    async def fetch_user_profile(self, uid: str) -> dict:

        try:
            base_crawler = await self.__create_base_crawler()
            if base_crawler is None:
                return {}

            async with base_crawler as crawler:
                # 通过模型生成基本请求参数
                params = UserProfile(mid=uid)
                # 创建请求endpoint
                generator = EndpointGenerator(params.dict())
                endpoint = await generator.user_profile_endpoint()
                # 发送请求，获取请求响应结果
                response = await crawler.fetch_get_json(endpoint)
            return response
        except Exception as e:
            print(e)
            return {}

    # 获取综合热门视频信息
    async def fetch_com_popular(self, pn: int) -> dict:

        # 获取请求头信息
        kwargs = await self.get_bilibili_headers()
        # 创建基础爬虫对象
        base_crawler = BaseCrawler(
            proxies=kwargs["proxies"], crawler_headers=kwargs["headers"]
        )
        async with base_crawler as crawler:
            # 通过模型生成基本请求参数
            params = ComPopular(pn=pn)
            # 创建请求endpoint
            generator = EndpointGenerator(params.dict())
            endpoint = await generator.com_popular_endpoint()
            # 发送请求，获取请求响应结果
            response = await crawler.fetch_get_json(endpoint)
        return response

    # 获取指定视频的评论
    async def fetch_video_comments(self, bv_id: str, pn: int) -> dict:

        # 评论排序 -- 1:按点赞数排序. 0:按时间顺序排序
        try:
            base_crawler = await self.__create_base_crawler()
            if base_crawler is None:
                return {}

            sort = 1
            async with base_crawler as crawler:
                # 创建请求endpoint
                endpoint = f"{BilibiliAPIEndpoints.VIDEO_COMMENTS}?type=1&oid={bv_id}&sort={sort}&nohot=0&ps=20&pn={pn}"
                # 发送请求，获取请求响应结果
                response = await crawler.fetch_get_json(endpoint)
            return response
        except Exception as e:
            print(e)
            return {}

    # 获取视频下指定评论的回复
    async def fetch_comment_reply(self, bv_id: str, pn: int, rpid: str) -> dict:
        """
        :param bv_id: 目标视频bv号
        :param pn: 页码
        :param rpid: 目标评论id，可通过fetch_video_comments获得
        :return:
        """
        try:
            base_crawler = await self.__create_base_crawler()
            if base_crawler is None:
                return {}

            async with base_crawler as crawler:
                # 创建请求endpoint
                endpoint = f"{BilibiliAPIEndpoints.COMMENT_REPLY}?type=1&oid={bv_id}&root={rpid}&&ps=20&pn={pn}"
                # 发送请求，获取请求响应结果
                response = await crawler.fetch_get_json(endpoint)
                return response
        except Exception as e:
            print(e)
            return {}

    # 获取指定用户动态
    async def fetch_user_dynamic(self, uid: str, offset: str) -> dict:
        # 获取请求头信息
        kwargs = await self.get_bilibili_headers()
        # 创建基础爬虫对象
        base_crawler = BaseCrawler(
            proxies=kwargs["proxies"], crawler_headers=kwargs["headers"]
        )
        async with base_crawler as crawler:
            # 通过模型生成基本请求参数
            params = UserDynamic(host_mid=uid, offset=offset)
            # 创建请求endpoint
            generator = EndpointGenerator(params.dict())
            endpoint = await generator.user_dynamic_endpoint()
            print(endpoint)
            # 发送请求，获取请求响应结果
            response = await crawler.fetch_get_json(endpoint)
        return response

    # 获取视频实时弹幕
    async def fetch_video_danmaku(self, cid: str):
        # 获取请求头信息
        try:
            base_crawler = await self.__create_base_crawler()
            if base_crawler is None:
                return {}

            async with base_crawler as crawler:
                # 创建请求endpoint
                endpoint = f"https://comment.bilibili.com/{cid}.xml"
                # 发送请求，获取请求响应结果
                response = await crawler.fetch_response(endpoint)
            return response.text
        except Exception as e:
            print(e)

    # 获取指定直播间信息
    async def fetch_live_room_detail(self, room_id: str) -> dict:
        # 获取请求头信息
        kwargs = await self.get_bilibili_headers()
        # 创建基础爬虫对象
        base_crawler = BaseCrawler(
            proxies=kwargs["proxies"], crawler_headers=kwargs["headers"]
        )
        async with base_crawler as crawler:
            # 创建请求endpoint
            endpoint = f"{BilibiliAPIEndpoints.LIVEROOM_DETAIL}?room_id={room_id}"
            # 发送请求，获取请求响应结果
            response = await crawler.fetch_get_json(endpoint)
        return response

    # 获取指定直播间视频流
    async def fetch_live_videos(self, room_id: str) -> dict:
        # 获取请求头信息
        kwargs = await self.get_bilibili_headers()
        # 创建基础爬虫对象
        base_crawler = BaseCrawler(
            proxies=kwargs["proxies"], crawler_headers=kwargs["headers"]
        )
        async with base_crawler as crawler:
            # 创建请求endpoint
            endpoint = f"{BilibiliAPIEndpoints.LIVE_VIDEOS}?cid={room_id}&quality=4"
            # 发送请求，获取请求响应结果
            response = await crawler.fetch_get_json(endpoint)
        return response

    # 获取指定分区正在直播的主播
    async def fetch_live_streamers(self, area_id: str, pn: int):
        # 获取请求头信息
        kwargs = await self.get_bilibili_headers()
        # 创建基础爬虫对象
        base_crawler = BaseCrawler(
            proxies=kwargs["proxies"], crawler_headers=kwargs["headers"]
        )
        async with base_crawler as crawler:
            # 创建请求endpoint
            endpoint = f"{BilibiliAPIEndpoints.LIVE_STREAMER}?platform=web&parent_area_id={area_id}&page={pn}"
            # 发送请求，获取请求响应结果
            response = await crawler.fetch_get_json(endpoint)
        return response

    "-------------------------------------------------------utils接口列表-------------------------------------------------------"

    # 通过bv号获得视频aid号
    async def bv_to_aid(self, bv_id: str) -> int:
        aid = await bv2av(bv_id=bv_id)
        return aid

    # 通过bv号获得视频分p信息
    async def fetch_video_parts(self, bv_id: str) -> str:
        # 获取请求头信息
        kwargs = await self.get_bilibili_headers()
        # 创建基础爬虫对象
        base_crawler = BaseCrawler(
            proxies=kwargs["proxies"], crawler_headers=kwargs["headers"]
        )
        async with base_crawler as crawler:
            # 创建请求endpoint
            endpoint = f"{BilibiliAPIEndpoints.VIDEO_PARTS}?bvid={bv_id}"
            # 发送请求，获取请求响应结果
            response = await crawler.fetch_get_json(endpoint)
        return response

    # 获取所有直播分区列表
    async def fetch_all_live_areas(self) -> dict:
        # 获取请求头信息
        kwargs = await self.get_bilibili_headers()
        # 创建基础爬虫对象
        base_crawler = BaseCrawler(
            proxies=kwargs["proxies"], crawler_headers=kwargs["headers"]
        )
        async with base_crawler as crawler:
            # 创建请求endpoint
            endpoint = BilibiliAPIEndpoints.LIVE_AREAS
            # 发送请求，获取请求响应结果
            response = await crawler.fetch_get_json(endpoint)
        return response

    "-------------------------------------------------------main-------------------------------------------------------"

    async def main(self):
        """-------------------------------------------------------handler接口列表-------------------------------------------------------"""

        # 获取单个作品数据
        # bv_id = 'BV1M1421t7hT'
        # result = await self.fetch_one_video(bv_id=bv_id)
        # print(result)

        # 获取视频流地址
        # bv_id = 'BV1y7411Q7Eq'
        # cid = '171776208'
        # result = await self.fetch_video_playurl(bv_id=bv_id, cid=cid)
        # print(result)

        # 获取用户发布作品数据
        # uid = '94510621'
        # pn = 1
        # result = await self.fetch_user_post_videos(uid=uid, pn=pn)
        # print(result)

        # 获取用户所有收藏夹信息
        # uid = '178360345'
        # reslut = await self.fetch_collect_folders(uid=uid)
        # print(reslut)

        # 获取用户指定收藏夹内视频数据
        # folder_id = '1756059545'  # 收藏夹id，可从<获取用户所有收藏夹信息>获得
        # pn = 1
        # result = await self.fetch_folder_videos(folder_id=folder_id, pn=pn)
        # print(result)

        # 获取指定用户的信息
        # uid = '178360345'
        # result = await self.fetch_user_profile(uid=uid)
        # print(result)

        # 获取综合热门信息
        # pn = 1  # 页码
        # result = await self.fetch_com_popular(pn=pn)
        # print(result)

        # 获取指定视频的评论(不登录只能获取一页的评论)
        # bv_id = "BV1QcLjzDEPn"
        # pn = 1
        # result = await self.fetch_video_comments(bv_id=bv_id, pn=pn)
        # print(result)

        # 获取视频下指定评论的回复(不登录只能获取一页的评论)
        # bv_id = "BV1M1421t7hT"
        # rpid = "************"
        # pn = 1
        # result = await self.fetch_comment_reply(bv_id=bv_id, pn=pn, rpid=rpid)
        # print(result)

        # 获取指定用户动态
        # uid = "16015678"
        # offset = ""     # 翻页索引，为空即从最新动态开始
        # result = await self.fetch_user_dynamic(uid=uid, offset=offset)
        # print(result)

        # 获取视频实时弹幕
        # cid = "1639235405"
        # result = await self.fetch_video_danmaku(cid=cid)
        # print(result)

        # 获取指定直播间信息
        # room_id = "1815229528"
        # result = await self.fetch_live_room_detail(room_id=room_id)
        # print(result)

        # 获取直播间视频流
        # room_id = "1815229528"
        # result = await self.fetch_live_videos(room_id=room_id)
        # print(result)

        # 获取指定分区正在直播的主播
        # pn = 1
        # area_id = '9'
        # result = await self.fetch_live_streamers(area_id=area_id, pn=pn)
        # print(result)

        "-------------------------------------------------------utils接口列表-------------------------------------------------------"
        # 通过bv号获得视频aid号
        # bv_id = 'BV1M1421t7hT'
        # aid = await self.get_aid(bv_id=bv_id)
        # print(aid)

        # 通过bv号获得视频分p信息
        # bv_id = "BV1vf421i7hV"
        # result = await self.fetch_video_parts(bv_id=bv_id)
        # print(result)

        # 获取所有直播分区列表
        # result = await self.fetch_all_live_areas()
        # print(result)

        search_key = await self.fetch_search_video(keyword="CSGO")
        print(search_key)


if __name__ == "__main__":
    # 初始化
    BilibiliWebCrawler = BilibiliWebCrawler(
        browser_api_key="CJMZeL7iaMRpTIKWn44e2dmaw0jNaOQI",
        proxy_auth_key="C1251FAB",
        proxy_auth_password="AF302A8D3FC1",
    )

    # 开始时间
    start = time.time()

    asyncio.run(BilibiliWebCrawler.main())

    # 结束时间
    end = time.time()
    print(f"耗时：{end - start}")
