import asyncio
import os
from urllib.parse import urlencode

import yaml

from crawlers.spiders.base_crawler import BaseCrawler
from crawlers.spiders.douyin.endpoints import DouyinAPIEndpoints
from crawlers.spiders.douyin.models import (
    PostDetail,
    UserCollection,
    UserLike,
    UserPost, UserMix, UserLive, UserLive2, LiveRoomRanking, UserProfile, PostComments, PostCommentsReply,
    BaseRequestModel, VideoSearch,
)
from crawlers.spiders.douyin.utils import (
    BogusManager,
)
from crawlers.utils.request_manager import RequestManager

# 配置文件路径
path = os.path.abspath(os.path.dirname(__file__))

# 读取配置文件
with open(f"{path}/config.yaml", "r", encoding="utf-8") as f:
    config = yaml.safe_load(f)


class DouyinWebCrawler:
    def __init__(
        self,
        browser_api_key: str = None,
        proxy_auth_key: str = None,
        proxy_auth_password: str = None,
        cookie_refresh_interval: int = None,
        proxy_refresh_interval: int = None,
        cache_type: str = "redis",
    ):
        """
        Args:
            browser_api_key: 浏览器API密钥(可选，从配置文件读取)
            proxy_auth_key: 代理认证密钥(可选，从配置文件读取)
            proxy_auth_password: 代理认证密码(可选，从配置文件读取)
            cookie_refresh_interval: cookie刷新间隔(分钟，可选，从配置文件读取)
            proxy_refresh_interval: 代理刷新间隔(分钟，可选，从配置文件读取)
            cache_type: 缓存类型，默认redis
        """
        # 从配置文件读取默认值
        proxy_config = config.get("ProxyConfig", {})
        browser_config = config.get("VirtualBrowserConfig", {})
        cookie_config = config.get("CookieConfig", {})

        # 使用传入参数或配置文件默认值
        self.browser_api_key = browser_api_key or browser_config.get(
            "browser_api_key", ""
        )
        self.proxy_auth_key = proxy_auth_key or proxy_config.get("proxy_auth_key", "")
        self.proxy_auth_password = proxy_auth_password or proxy_config.get(
            "proxy_auth_password", ""
        )
        self.cookie_refresh_interval = cookie_refresh_interval or cookie_config.get(
            "cookie_refresh_interval", 30
        )
        self.proxy_refresh_interval = proxy_refresh_interval or proxy_config.get(
            "proxy_refresh_interval", 1
        )

        # 初始化RequestManager
        self.request_manager = RequestManager(
            browser_api_key=self.browser_api_key,
            proxy_auth_key=self.proxy_auth_key,
            proxy_auth_password=self.proxy_auth_password,
            cookie_refresh_interval=self.cookie_refresh_interval,
            proxy_refresh_interval=self.proxy_refresh_interval,
            cache_type=cache_type,
        )

    def get_config_info(self) -> dict:
        """
        获取当前配置信息

        Returns:
            包含配置信息的字典
        """
        return {
            "browser_api_key": (
                self.browser_api_key[:10] + "..." if self.browser_api_key else "未配置"
            ),
            "proxy_auth_key": (
                self.proxy_auth_key[:10] + "..." if self.proxy_auth_key else "未配置"
            ),
            "proxy_auth_password": "***" if self.proxy_auth_password else "未配置",
            "cookie_refresh_interval": self.cookie_refresh_interval,
            "proxy_refresh_interval": self.proxy_refresh_interval,
        }

    def print_config_info(self):
        """
        打印当前配置信息
        """
        config_info = self.get_config_info()
        print("=== DouyinWebCrawler 配置信息 ===")
        for key, value in config_info.items():
            print(f"{key}: {value}")
        print("=" * 35)

    async def get_douyin_headers(self, cookie: str = None, proxy: str = None):
        """获取抖音请求头"""
        douyin_config = config["TokenManager"]["douyin"]

        if not cookie:
            cookie = douyin_config["headers"]["Cookie"]

        kwargs = {
            "headers": {
                "Accept-Language": douyin_config["headers"]["Accept-Language"],
                "User-Agent": douyin_config["headers"]["User-Agent"],
                "Referer": douyin_config["headers"]["Referer"],
                "Cookie": cookie,
            },
            "proxies": {"http://": proxy, "https://": proxy} if proxy else None,
        }
        return kwargs

    async def fetch_one_video(self, aweme_id: str):
        """获取单个视频信息"""
        cookie, proxy = await self.request_manager.prepare_request("douyin")

        try:
            kwargs = await self.get_douyin_headers(cookie=cookie, proxy=proxy)
            base_crawler = BaseCrawler(
                proxies=kwargs["proxies"], crawler_headers=kwargs["headers"]
            )

            async with base_crawler as crawler:
                params = PostDetail(aweme_id=aweme_id)
                params_dict = params.dict()
                a_bogus = BogusManager.ab_model_2_endpoint(
                    params_dict, kwargs["headers"]["User-Agent"]
                )
                endpoint = f"{DouyinAPIEndpoints.POST_DETAIL}?{urlencode(params_dict)}&a_bogus={a_bogus}"

                response = await crawler.fetch_get_json(endpoint)

                if response:
                    self.request_manager.mark_request_success("douyin")
                    return response
                else:
                    self.request_manager.mark_request_failed("douyin")
                    return None

        except Exception as e:
            print(f"获取视频信息失败: {e}")
            self.request_manager.mark_request_failed("douyin")
            return None

    # publish_time 0 7 14 180
    # sort_type :
    async def fetch_search_video(
        self,
        keyword: str,
        offset: int = 0,
        sort_type: int = None,
        publish_time: int = None,
        search_id: str = "",
    ):
        """获取单个视频信息"""
        cookie, proxy = await self.request_manager.prepare_request("douyin")

        try:
            kwargs = await self.get_douyin_headers(cookie=cookie, proxy=proxy)
            base_crawler = BaseCrawler(
                proxies=kwargs["proxies"], crawler_headers=kwargs["headers"]
            )

            async with base_crawler as crawler:
                params = VideoSearch(
                    keyword=keyword, offset=offset, search_id=search_id
                )
                params_dict = params.dict()

                a_bogus = BogusManager.ab_model_2_endpoint(
                    params_dict, kwargs["headers"]["User-Agent"]
                )
                kwargs["headers"][
                    "Referer"
                ] = f"https://www.douyin.com/search/{keyword}?aid=f594bbd9-a0e2-4651-9319-ebe3cb6298c1&type=general"

                self.__parse_search_params(
                    sort_type=sort_type, publish_time=publish_time, params=params_dict
                )
                endpoint = f"{DouyinAPIEndpoints.VIDEO_SEARCH}?{urlencode(params_dict)}&a_bogus={a_bogus}"
                response = await crawler.fetch_get_json(endpoint)

                if response:
                    self.request_manager.mark_request_success("douyin")
                    return response
                else:
                    self.request_manager.mark_request_failed("douyin")
                    return None

        except Exception as e:
            print(f"获取视频信息失败: {e}")
            self.request_manager.mark_request_failed("douyin")
            return None

    def __parse_search_params(self, sort_type, publish_time, params):
        """
        设置搜索参数
        """
        params["msToken"] = ""
        params["uifid"] = ""

        if sort_type:
            params |= {
                "sort_type": f"{sort_type}",
                "is_filter_search": "1",
            }
        if publish_time:
            params |= {
                "publish_time": f"{publish_time}",
                "is_filter_search": "1",
            }

    async def fetch_user_post_videos(
        self, sec_user_id: str, max_cursor: int, count: int
    ):
        """获取用户发布的视频列表"""
        cookie, proxy = await self.request_manager.prepare_request("douyin")

        try:
            kwargs = await self.get_douyin_headers(cookie=cookie, proxy=proxy)
            base_crawler = BaseCrawler(
                proxies=kwargs["proxies"], crawler_headers=kwargs["headers"]
            )

            async with base_crawler as crawler:
                params = UserPost(
                    sec_user_id=sec_user_id, max_cursor=max_cursor, count=count
                )

                params_dict = params.dict()
                params_dict["msToken"] = ""
                a_bogus = BogusManager.ab_model_2_endpoint(
                    params_dict, kwargs["headers"]["User-Agent"]
                )
                endpoint = f"{DouyinAPIEndpoints.USER_POST}?{urlencode(params_dict)}&a_bogus={a_bogus}"

                response = await crawler.fetch_get_json(endpoint)

                if response:
                    self.request_manager.mark_request_success("douyin")
                    return response
                else:
                    self.request_manager.mark_request_failed("douyin")
                    return None

        except Exception as e:
            print(f"获取用户视频列表失败: {e}")
            self.request_manager.mark_request_failed("douyin")
            return None

    async def fetch_user_like_videos(
        self, sec_user_id: str, max_cursor: int, count: int
    ):
        """获取用户点赞的视频列表"""
        cookie, proxy = await self.request_manager.prepare_request("douyin")

        try:
            kwargs = await self.get_douyin_headers(cookie=cookie, proxy=proxy)
            base_crawler = BaseCrawler(
                proxies=kwargs["proxies"], crawler_headers=kwargs["headers"]
            )

            async with base_crawler as crawler:
                params = UserLike(
                    sec_user_id=sec_user_id, max_cursor=max_cursor, count=count
                )

                params_dict = params.dict()
                params_dict["msToken"] = ""
                a_bogus = BogusManager.ab_model_2_endpoint(
                    params_dict, kwargs["headers"]["User-Agent"]
                )
                endpoint = f"{DouyinAPIEndpoints.USER_FAVORITE_A}?{urlencode(params_dict)}&a_bogus={a_bogus}"

                response = await crawler.fetch_get_json(endpoint)

                if response:
                    self.request_manager.mark_request_success("douyin")
                    return response
                else:
                    self.request_manager.mark_request_failed("douyin")
                    return None

        except Exception as e:
            print(f"获取用户点赞视频列表失败: {e}")
            self.request_manager.mark_request_failed("douyin")
            return None

    async def fetch_user_collection_videos(self, cursor: int = 0, count: int = 20):
        """
        获取用户收藏的视频列表
        获取用户收藏作品数据（用户提供自己的Cookie）
        """
        cookie, proxy = await self.request_manager.prepare_request("douyin")

        kwargs = await self.get_douyin_headers(cookie=cookie, proxy=proxy)

        kwargs["headers"]["Cookie"] = cookie
        base_crawler = BaseCrawler(
            proxies=kwargs["proxies"], crawler_headers=kwargs["headers"]
        )
        async with base_crawler as crawler:
            params = UserCollection(cursor=cursor, count=count)
            endpoint = BogusManager.xb_model_2_endpoint(
                DouyinAPIEndpoints.USER_COLLECTION,
                params.dict(),
                kwargs["headers"]["User-Agent"],
            )
            response = await crawler.fetch_post_json(endpoint)

            if response:
                self.request_manager.mark_request_success("douyin")
                return response
            else:
                self.request_manager.mark_request_failed("douyin")
                return None
        return response

    async def fetch_user_mix_videos(
        self, mix_id: str, cursor: int = 0, count: int = 20
    ):
        """
        获取用户合集的视频列表
        """
        cookie, proxy = await self.request_manager.prepare_request("douyin")
        kwargs = await self.get_douyin_headers(cookie=cookie, proxy=proxy)

        base_crawler = BaseCrawler(
            proxies=kwargs["proxies"], crawler_headers=kwargs["headers"]
        )
        async with base_crawler as crawler:
            params = UserMix(mix_id=mix_id, cursor=cursor, count=count)
            endpoint = BogusManager.xb_model_2_endpoint(
                DouyinAPIEndpoints.MIX_AWEME,
                params.dict(),
                kwargs["headers"]["User-Agent"],
            )
            response = await crawler.fetch_get_json(endpoint)
        return response

    async def fetch_user_live_videos(self, webcast_id: str, room_id_str=""):
        """
        获取用户直播流数据
        """

        cookie, proxy = await self.request_manager.prepare_request("douyin")
        kwargs = await self.get_douyin_headers(cookie=cookie, proxy=proxy)

        base_crawler = BaseCrawler(
            proxies=kwargs["proxies"], crawler_headers=kwargs["headers"]
        )

        async with base_crawler as crawler:
            params = UserLive(web_rid=webcast_id, room_id_str=room_id_str)
            endpoint = BogusManager.xb_model_2_endpoint(
                DouyinAPIEndpoints.LIVE_INFO,
                params.dict(),
                kwargs["headers"]["User-Agent"],
            )
            response = await crawler.fetch_get_json(endpoint)
        return response

    async def fetch_user_live_videos_by_room_id(self, room_id: str):
        """
        获取指定用户的直播流数据
        """

        cookie, proxy = await self.request_manager.prepare_request("douyin")
        kwargs = await self.get_douyin_headers(cookie=cookie, proxy=proxy)

        base_crawler = BaseCrawler(
            proxies=kwargs["proxies"], crawler_headers=kwargs["headers"]
        )

        async with base_crawler as crawler:
            params = UserLive2(room_id=room_id)
            endpoint = BogusManager.xb_model_2_endpoint(
                DouyinAPIEndpoints.LIVE_INFO_ROOM_ID,
                params.dict(),
                kwargs["headers"]["User-Agent"],
            )
            response = await crawler.fetch_get_json(endpoint)
        return response

    async def fetch_live_gift_ranking(self, room_id: str, rank_type: int = 30):
        """
        获取直播间送礼用户排行榜
        """
        cookie, proxy = await self.request_manager.prepare_request("douyin")
        kwargs = await self.get_douyin_headers(cookie=cookie, proxy=proxy)

        base_crawler = BaseCrawler(
            proxies=kwargs["proxies"], crawler_headers=kwargs["headers"]
        )
        async with base_crawler as crawler:
            params = LiveRoomRanking(room_id=room_id, rank_type=rank_type)
            endpoint = BogusManager.xb_model_2_endpoint(
                DouyinAPIEndpoints.LIVE_GIFT_RANK,
                params.dict(),
                kwargs["headers"]["User-Agent"],
            )
            response = await crawler.fetch_get_json(endpoint)
        return response

    async def handler_user_profile(self, sec_user_id: str):
        """
        获取指定用户的信息
        """
        cookie, proxy = await self.request_manager.prepare_request("douyin")
        kwargs = await self.get_douyin_headers(cookie=cookie, proxy=proxy)

        base_crawler = BaseCrawler(
            proxies=kwargs["proxies"], crawler_headers=kwargs["headers"]
        )
        async with base_crawler as crawler:
            params = UserProfile(sec_user_id=sec_user_id)
            endpoint = BogusManager.xb_model_2_endpoint(
                DouyinAPIEndpoints.USER_DETAIL,
                params.dict(),
                kwargs["headers"]["User-Agent"],
            )
            response = await crawler.fetch_get_json(endpoint)
        return response

    # 获取指定视频的评论数据
    async def fetch_video_comments(
        self, aweme_id: str, cursor: int = 0, count: int = 20
    ):
        """
        获取指定视频的评论数据
        """
        try:
            cookie, proxy = await self.request_manager.prepare_request("douyin")
            kwargs = await self.get_douyin_headers(cookie=cookie, proxy=proxy)

            base_crawler = BaseCrawler(
                proxies=kwargs["proxies"], crawler_headers=kwargs["headers"]
            )
            async with base_crawler as crawler:
                params = PostComments(aweme_id=aweme_id, cursor=cursor, count=count)
                endpoint = BogusManager.xb_model_2_endpoint(
                    DouyinAPIEndpoints.POST_COMMENT,
                    params.dict(),
                    kwargs["headers"]["User-Agent"],
                )
                response = await crawler.fetch_get_json(endpoint)
                if response:
                    self.request_manager.mark_request_success("douyin")
                    return response
                else:
                    self.request_manager.mark_request_failed("douyin")
                    return None
        except Exception as e:
            print(f"Error fetching video comments: {e}")

    async def fetch_video_comments_reply(
        self, item_id: str, comment_id: str, cursor: int = 0, count: int = 20
    ):
        """
        获取指定视频的评论回复数据
        """
        cookie, proxy = await self.request_manager.prepare_request("douyin")
        kwargs = await self.get_douyin_headers(cookie=cookie, proxy=proxy)

        base_crawler = BaseCrawler(
            proxies=kwargs["proxies"], crawler_headers=kwargs["headers"]
        )

        async with base_crawler as crawler:
            params = PostCommentsReply(
                item_id=item_id, comment_id=comment_id, cursor=cursor, count=count
            )
            endpoint = BogusManager.xb_model_2_endpoint(
                DouyinAPIEndpoints.POST_COMMENT_REPLY,
                params.dict(),
                kwargs["headers"]["User-Agent"],
            )
            response = await crawler.fetch_get_json(endpoint)
        return response

    async def fetch_hot_search_result(self):
        """
        获取抖音热榜数据
        """
        cookie, proxy = await self.request_manager.prepare_request("douyin")
        kwargs = await self.get_douyin_headers(cookie=cookie, proxy=proxy)

        base_crawler = BaseCrawler(
            proxies=kwargs["proxies"], crawler_headers=kwargs["headers"]
        )
        async with base_crawler as crawler:
            params = BaseRequestModel()
            endpoint = BogusManager.xb_model_2_endpoint(
                DouyinAPIEndpoints.DOUYIN_HOT_SEARCH,
                params.dict(),
                kwargs["headers"]["User-Agent"],
            )
            response = await crawler.fetch_get_json(endpoint)
        return response

    # Utils方法保持不变...

    async def demo(self):
        """示例使用方法"""
        # 获取用户发布的视频
        # sec_user_id = "MS4wLjABAAAAleeKShVS3RFhB8D6rd9Zs1IatJaEFikEjdNO3QlRRfGiTdvqpoem_IBcyPQP0ltS"
        # max_cursor = 0
        # count = 10
        # result = await self.fetch_user_post_videos(sec_user_id, max_cursor, count)
        # print(result)

        # video_one = await self.fetch_one_video(aweme_id="7502658388942179599")
        # print(video_one)

        # user_info = await self.handler_user_profile(
        #     sec_user_id="MS4wLjABAAAAleeKShVS3RFhB8D6rd9Zs1IatJaEFikEjdNO3QlRRfGiTdvqpoem_IBcyPQP0ltS")
        # print(user_info)

        response = await self.fetch_one_video(
            aweme_id="6804735432291552525"
        )
        print(response)
        # rs = await self.fetch_search_video(keyword="美食")


if __name__ == "__main__":
    # 初始化爬虫（使用配置文件默认值）
    crawler = DouyinWebCrawler()

    # 打印配置信息
    crawler.print_config_info()

    # 或者可以覆盖特定参数
    # crawler = DouyinWebCrawler(
    #     browser_api_key="your_custom_browser_key",
    #     proxy_auth_key="your_custom_proxy_key",
    #     proxy_auth_password="your_custom_proxy_password",
    # )

    # 运行示例
    asyncio.run(crawler.demo())
